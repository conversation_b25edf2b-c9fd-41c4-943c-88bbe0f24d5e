"""Anime details scraping functionality."""
from typing import Dict, Optional, Union
import json
import cloudscraper
from bs4 import BeautifulSoup

from src.management import get_logger
from src.utils.constants import (
    SRC_BASE_URL,
    USER_AGENT_HEADER,
    ACCEPT_HEADER,
    ACCEPT_ENCODING_HEADER
)
from src.utils import (
    extract_episodes,
    extract_base_anime_info,
    extract_text,
    extract_attribute,
    extract_href_id,
    safe_int_extract
)
from src.models import (
    AnimeStats,
    AnimeInfo,
    Season,
    RecommendedAnime,
    PromotionalVideo,
    Character,
    VoiceActor,
    CharacterVoiceActor,
    EpisodeInfo
)

# Configure logging
logger = get_logger("AnimeAboutInfo")

def extract_season_info(element: BeautifulSoup) -> Season:
    """Extract season information from HTML element."""
    season = Season()
    
    # Extract season ID
    season.id = extract_href_id(element, "a")
    
    # Extract season name/title
    name = extract_text(element, ".title")
    if name:
        season.title = name
        season.name = name
    
    # Extract season poster
    poster_elem = element.select_one(".season-poster")
    if poster_elem and "style" in poster_elem.attrs:
        style = poster_elem["style"]
        if "background-image: url(" in style:
            season.poster = style.split("url(")[1].split(")")[0].strip("'\"")
    
    # Check if current season
    season.isCurrent = "active" in element.get("class", [])
    
    return season

def extract_character_voice_actor(element: BeautifulSoup) -> Optional[CharacterVoiceActor]:
    """Extract character and voice actor information from HTML element."""
    char_name = extract_text(element, ".pi-name a")
    char_role = extract_text(element, ".pi-cast")
    
    if not (char_name and char_role):
        return None
    
    # Extract character info
    char = Character(
        id=extract_href_id(element, ".pi-name a") or "",
        name=char_name,
        cast=char_role,
        poster=extract_attribute(element, ".per-info.ltr .pi-avatar img", "data-src") or ""
    )
    
    # Extract voice actor info
    va = VoiceActor(
        id=extract_href_id(element, ".per-info.rtl .pi-detail a") or "",
        name=extract_text(element, ".per-info.rtl .pi-detail a") or "",
        cast=extract_text(element, ".per-info.rtl .pi-cast") or "",
        poster=extract_attribute(element, ".per-info.rtl .pi-avatar img", "data-src") or ""
    )
    
    return CharacterVoiceActor(character=char, voiceActor=va)

def extract_promotional_video(element: BeautifulSoup) -> Optional[PromotionalVideo]:
    """Extract promotional video information from HTML element."""
    title = extract_text(element, ".sii-title")
    if not title:
        return None
    
    return PromotionalVideo(
        title=title,
        source=element.get("data-src", "").strip(),
        thumbnail=extract_attribute(element, "img", "src")
    )

def get_anime_about_info(anime_id: str) -> Optional[Dict[str, Union[Dict, bool]]]:
    """Get detailed information about an anime."""
    # Validate anime ID
    if not anime_id or not anime_id.strip():
        raise ValueError("Anime ID cannot be empty")

    anime_id = anime_id.strip()

    # Basic validation - anime IDs should contain at least one hyphen
    if "-" not in anime_id:
        logger.warning(f"Anime ID '{anime_id}' may be invalid (no hyphens found)")

    # Remove any leading/trailing slashes
    anime_id = anime_id.strip("/")

    # Construct URL properly
    anime_url = f"{SRC_BASE_URL}/{anime_id}"
    logger.debug(f"Fetching anime info for ID: '{anime_id}'")
    logger.debug(f"URL: {anime_url}")
    
    try:
        # Use cloudscraper to bypass Cloudflare protection
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'windows',
                'mobile': False
            }
        )
        
        # Set headers
        scraper.headers.update({
            'User-Agent': USER_AGENT_HEADER,
            'Accept': ACCEPT_HEADER,
            'Accept-Encoding': ACCEPT_ENCODING_HEADER,
            'Accept-Language': 'en-US,en;q=0.9',
            'Cache-Control': 'no-cache',
            'Pragma': 'no-cache',
            'Sec-Ch-Ua': '"Not.A/Brand";v="8", "Chromium";v="114", "Google Chrome";v="114"',
            'Sec-Ch-Ua-Mobile': '?0',
            'Sec-Ch-Ua-Platform': '"Windows"',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Upgrade-Insecure-Requests': '1'
        })
        
        response = scraper.get(anime_url)
        response.raise_for_status()
        
        # Parse with BeautifulSoup
        soup = BeautifulSoup(response.text, 'lxml')
        
        result = {
            "success": True,
            "data": {
                "anime": {
                    "info": AnimeInfo(id=anime_id),
                    "moreInfo": {
                        "genres": [],
                        "studios": []
                    }
                },
                "seasons": [],
                "mostPopularAnimes": [],
                "relatedAnimes": [],
                "recommendedAnimes": []
            }
        }
        
        info = result["data"]["anime"]["info"]
        
        # Extract sync data for IDs
        sync_data = soup.find("script", id="syncData")
        if sync_data and sync_data.text:
            try:
                sync_json = json.loads(sync_data.text)
                info.anilistId = safe_int_extract(sync_json.get("anilist_id", "0"))
                info.malId = safe_int_extract(sync_json.get("mal_id", "0"))
            except json.JSONDecodeError:
                logger.warning("Failed to parse sync data as JSON")
        
        # Extract main content with multiple fallback selectors
        content_selectors = [
            "#ani_detail .container .anis-content",
            "#ani_detail .anis-content",
            ".container .anis-content",
            ".anis-content",
            "#ani_detail .container",
            "#ani_detail",
            ".anime-detail",
            ".detail-content"
        ]

        content = None
        used_selector = None

        for selector in content_selectors:
            content = soup.select_one(selector)
            if content:
                used_selector = selector
                logger.debug(f"Found content using selector: {selector}")
                break

        if not content:
            # Log available elements for debugging
            logger.warning("Could not find main content section with any selector")
            logger.debug("Available top-level elements:")
            for elem in soup.find_all(recursive=False):
                if hasattr(elem, 'name') and elem.name:
                    logger.debug(f"  - {elem.name}: {elem.get('id', '')} {elem.get('class', [])}")

            # Log page title for debugging
            title = soup.find('title')
            if title:
                logger.debug(f"Page title: {title.text.strip()}")

            # Check for error indicators
            page_text = response.text.lower()
            error_indicators = ["404", "not found", "page not found", "error", "blocked", "access denied", "forbidden"]
            found_errors = [error for error in error_indicators if error in page_text]
            if found_errors:
                logger.warning(f"Potential error indicators found in page: {found_errors}")

            # Check if we can find any anime-specific content before falling back
            anime_indicators = [
                ".film-name", ".dynamic-name", ".anime-title",
                ".film-description", ".anime-description",
                ".anisc-detail", ".anime-detail",
                ".film-stats", ".anime-stats"
            ]

            has_anime_content = False
            for indicator in anime_indicators:
                element = soup.select_one(indicator)
                if element:
                    has_anime_content = True
                    logger.debug(f"Found anime content indicator: {indicator}")
                    if element.text.strip():
                        logger.debug(f"  Content preview: {element.text.strip()[:100]}...")
                    break

            if has_anime_content:
                # Try to find any content with anime information
                possible_content = soup.select_one("body") or soup
                if possible_content:
                    logger.debug("Attempting to extract from body/root element with anime content")
                    content = possible_content
                    used_selector = "body/root fallback"
                else:
                    logger.error("No content found at all")
                    return None
            else:
                logger.error("No anime-specific content found on page")
                logger.debug(f"Checked indicators: {anime_indicators}")

                # Save HTML for debugging if in debug mode
                try:
                    debug_file = f"debug_anime_{anime_id.replace('/', '_').replace('-', '_')}.html"
                    with open(debug_file, 'w', encoding='utf-8') as f:
                        f.write(response.text)
                    logger.debug(f"Saved HTML content to {debug_file} for debugging")
                except Exception as save_error:
                    logger.debug(f"Could not save debug HTML: {save_error}")

                return None

        if content:
            logger.debug(f"Processing content found with selector: {used_selector}")

            # Extract base info
            anime_info = extract_base_anime_info(content)
            info.name = anime_info.get("name")
            info.poster = anime_info.get("poster")

            # Extract description with multiple selectors
            description_selectors = [
                ".anisc-detail .film-description .text",
                ".film-description .text",
                ".description .text",
                ".anime-description",
                ".synopsis"
            ]

            for desc_selector in description_selectors:
                description = extract_text(content, desc_selector)
                if description:
                    info.description = description
                    break

            # Extract stats with fallback selectors
            stats_selectors = [".film-stats", ".anime-stats", ".stats"]
            stats = None

            for stats_selector in stats_selectors:
                stats = content.select_one(stats_selector)
                if stats:
                    break

            if stats:
                info.stats.rating = extract_text(stats, ".tick .tick-pg") or extract_text(stats, ".rating")
                info.stats.quality = extract_text(stats, ".tick .tick-quality") or extract_text(stats, ".quality")

                # Type and Duration
                tick_type = extract_text(stats, ".tick")
                if tick_type:
                    type_text = tick_type.strip().replace("\n", " ").split()
                    if len(type_text) >= 2:
                        info.stats.type = type_text[-2]
                        info.stats.duration = type_text[-1]

                # Episodes
                eps = extract_episodes(stats)
                info.stats.episodes["sub"] = eps.sub
                info.stats.episodes["dub"] = eps.dub

            # Extract genres and studios with fallback selectors
            genre_selectors = [
                ".anisc-info .item-list a",
                ".anime-info .genres a",
                ".genres a",
                ".genre-list a"
            ]

            genres = []
            for genre_selector in genre_selectors:
                genre_elements = content.select(genre_selector)
                if genre_elements:
                    genres = [genre.text.strip() for genre in genre_elements]
                    break
            result["data"]["anime"]["moreInfo"]["genres"] = genres

            studio_selectors = [
                ".anisc-info .item-title a.name",
                ".anime-info .studios a",
                ".studios a",
                ".studio-list a"
            ]

            studios = []
            for studio_selector in studio_selectors:
                studio_elements = content.select(studio_selector)
                if studio_elements:
                    studios = [studio.text.strip() for studio in studio_elements]
                    break
            result["data"]["anime"]["moreInfo"]["studios"] = studios

            # Extract seasons
            seasons = []
            season_selectors = [
                ".block_area-seasons .os-item",
                ".seasons .season-item",
                ".season-list .item"
            ]

            for season_selector in season_selectors:
                season_elements = soup.select(season_selector)
                if season_elements:
                    for season_elem in season_elements:
                        season = extract_season_info(season_elem)
                        if season.id:  # Only add if we got a valid ID
                            seasons.append(season)
                    break
            result["data"]["seasons"] = seasons

            # Extract characters and voice actors
            cva_list = []
            cva_selectors = [
                ".block-actors-content .bac-item",
                ".characters .character-item",
                ".cast-list .cast-item"
            ]

            for cva_selector in cva_selectors:
                char_elements = soup.select(cva_selector)
                if char_elements:
                    for char_elem in char_elements:
                        cva = extract_character_voice_actor(char_elem)
                        if cva:
                            cva_list.append(cva)
                    break
            info.charactersVoiceActors = cva_list

            # Extract promotional videos
            promo_list = []
            promo_selectors = [
                ".block_area-promotions-list .item",
                ".promotions .promo-item",
                ".videos .video-item"
            ]

            for promo_selector in promo_selectors:
                promo_elements = soup.select(promo_selector)
                if promo_elements:
                    for promo_elem in promo_elements:
                        promo = extract_promotional_video(promo_elem)
                        if promo:
                            promo_list.append(promo)
                    break
            info.promotionalVideos = promo_list

            # Extract recommended animes
            recommended = []
            rec_selectors = [
                ".block_area_category .flw-item",
                ".recommendations .anime-item",
                ".related-anime .item"
            ]

            for rec_selector in rec_selectors:
                rec_elements = soup.select(rec_selector)
                if rec_elements:
                    for rec_elem in rec_elements:
                        anime_info = extract_base_anime_info(rec_elem)
                        if anime_info:
                            rec = RecommendedAnime(
                                id=anime_info.get("id"),
                                name=anime_info.get("name"),
                                jname=anime_info.get("jname"),
                                poster=anime_info.get("poster"),
                                type=anime_info.get("type")
                            )

                            duration = extract_text(rec_elem, ".fd-infor .fdi-duration") or extract_text(rec_elem, ".duration")
                            if duration:
                                rec.duration = duration.strip()

                            # Extract episodes
                            eps = extract_episodes(rec_elem)
                            rec.episodes["sub"] = eps.sub
                            rec.episodes["dub"] = eps.dub
                            rec.episodes["total"] = eps.total

                            recommended.append(rec)
                    break

            result["data"]["recommendedAnimes"] = recommended

            # Validate that we extracted meaningful data
            has_meaningful_data = (
                info.name or
                info.description or
                info.poster or
                result["data"]["anime"]["moreInfo"]["genres"] or
                result["data"]["seasons"] or
                info.charactersVoiceActors or
                recommended
            )

            if has_meaningful_data:
                logger.debug(f"Successfully extracted anime info: name='{info.name}', description_length={len(info.description or '')}")
                return result
            else:
                logger.warning("No meaningful anime data was extracted from the page")
                return None
        else:
            logger.error("Could not find any usable content section")
            return None
        
    except Exception as e:
        logger.error(f"Error occurred while fetching anime info for '{anime_id}': {str(e)}")
        logger.debug(f"URL attempted: {anime_url}")

        # Try to provide more specific error information
        if "404" in str(e) or "Not Found" in str(e):
            logger.error(f"Anime with ID '{anime_id}' not found (404)")
            raise ValueError(f"Anime with ID '{anime_id}' not found")
        elif "403" in str(e) or "Forbidden" in str(e):
            logger.error(f"Access forbidden when fetching anime '{anime_id}' (403)")
            raise ValueError(f"Access forbidden for anime '{anime_id}'")
        elif "timeout" in str(e).lower():
            logger.error(f"Timeout when fetching anime '{anime_id}'")
            raise ValueError(f"Timeout when fetching anime '{anime_id}'")
        else:
            raise ValueError(f"Failed to fetch anime information: {str(e)}")
